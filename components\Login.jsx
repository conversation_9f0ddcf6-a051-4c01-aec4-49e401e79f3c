import { View, Text, Image, TouchableOpacity } from 'react-native'
import React from 'react'
import { StyleSheet } from 'react-native'
import { Colors } from '@/constants/Colors'
import { useRouter } from 'expo-router'

export default function Login() {

  const router = useRouter();
  return (
    <View>
      <Image source={require('./../assets/images/login.png')}
        style={{ 
          width: '100%',
          height: 500
        }}
        />
        <View style={styles.container}>
            <Text style={{
                fontSize: 25,
                fontFamily: 'roboto-bold',
                color: '#1F2937',
                textAlign: 'center',
                marginTop: 10
            }}>Welcome to Wanderlust</Text>

            <Text style={{
                fontSize: 17,
                fontFamily: 'roboto',
                color:Colors.GRAY,
                textAlign: 'center',
                marginTop: 10
            }}>The intelligent travel planner that designs your perfect trip. Simply tell us your dream, and our AI will handle the rest—from itinerary to booking</Text>

            <TouchableOpacity style={styles.button}
            onPress={() => router.push('/auth/sign-in')}
            >
                <Text style={{
                    fontSize: 15,
                    fontFamily: 'roboto-medium',
                    color: '#fff',
                    textAlign: 'center',
                }}>Get Started</Text>
            </TouchableOpacity>
        </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container:{
    backgroundColor:Colors.SECONDARY,
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '100%',
    padding: 15
  },
  button:{
    padding: 15,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 99,
    marginTop: '25%'
  }
})